package com.akd.library.service.impl;

import com.akd.library.model.Book;
import com.akd.library.repository.BookRepository;
import com.akd.library.service.BookService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by am<PERSON><PERSON>
 * User: ak139
 * Date: 15/07/25
 */
@Service
public class BookServiceImpl implements BookService {


    private final BookRepository bookRepository;

    public BookServiceImpl(BookRepository bookRepository) {
        this.bookRepository = bookRepository;
    }

    /**
     * @param id
     * @return
     */
    @Override
    public Book bookDetails(int id) {
        return bookRepository.findById(id).orElse(null);
    }

    /**
     * @return
     */
    @Override
    public List<Book> listAllBooks() {
        return bookRepository.findAll();
    }

    /**
     * @param book
     */
    @Override
    public void addBook(Book book) {
        bookRepository.save(book);
    }
}
