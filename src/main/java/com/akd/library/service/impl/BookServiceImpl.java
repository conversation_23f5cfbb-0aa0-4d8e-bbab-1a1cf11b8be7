package com.akd.library.service.impl;

import com.akd.library.exception.BookNotFoundException;
import com.akd.library.model.Book;
import com.akd.library.repository.BookRepository;
import com.akd.library.service.BookService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Implementation of BookService interface
 * Created by amitkumar
 * User: ak139
 * Date: 15/07/25
 */
@Service
@Transactional
public class BookServiceImpl implements BookService {

    private final BookRepository bookRepository;

    public BookServiceImpl(BookRepository bookRepository) {
        this.bookRepository = bookRepository;
    }

    /**
     * Get book details by ID
     * @param id Book ID
     * @return Book details
     * @throws BookNotFoundException if book not found
     */
    @Override
    @Transactional(readOnly = true)
    public Book bookDetails(int id) {
        return bookRepository.findById(id)
                .orElseThrow(() -> new BookNotFoundException("Book not found with id: " + id));
    }

    /**
     * Get all books
     * @return List of all books
     */
    @Override
    @Transactional(readOnly = true)
    public List<Book> listAllBooks() {
        return bookRepository.findAll();
    }

    /**
     * Add a new book
     * @param book Book to be added
     * @return Saved book
     */
    @Override
    public Book addBook(Book book) {
        validateBook(book);
        return bookRepository.save(book);
    }

    /**
     * Update book details
     * @param book Book with updated details
     * @return Updated book
     * @throws BookNotFoundException if book not found
     */
    @Override
    public Book updateBookDetails(Book book) {
        if (!bookRepository.existsById(book.getBookId())) {
            throw new BookNotFoundException("Book not found with id: " + book.getBookId());
        }
        validateBook(book);
        return bookRepository.save(book);
    }

    /**
     * Delete a book
     * @param id Book ID
     * @throws BookNotFoundException if book not found
     */
    @Override
    public void deleteBook(int id) {
        if (!bookRepository.existsById(id)) {
            throw new BookNotFoundException("Book not found with id: " + id);
        }
        bookRepository.deleteById(id);
    }

    /**
     * Search books by title
     * @param bookTitle Book title to search
     * @return List of books matching the title
     */
    @Override
    @Transactional(readOnly = true)
    public List<Book> searchBookByTitle(String bookTitle) {
        if (bookTitle == null || bookTitle.trim().isEmpty()) {
            throw new IllegalArgumentException("Book title cannot be null or empty");
        }
        return bookRepository.findByTitleContainingIgnoreCase(bookTitle);
    }

    /**
     * Search books by ISBN
     * @param isbn ISBN to search
     * @return List of books matching the ISBN
     */
    @Override
    @Transactional(readOnly = true)
    public List<Book> searchBookByISBN(String isbn) {
        if (isbn == null || isbn.trim().isEmpty()) {
            throw new IllegalArgumentException("ISBN cannot be null or empty");
        }
        return bookRepository.findByIsbnContainingIgnoreCase(isbn);
    }

    /**
     * Validate book data
     * @param book Book to validate
     */
    private void validateBook(Book book) {
        if (book.getTitle() == null || book.getTitle().trim().isEmpty()) {
            throw new IllegalArgumentException("Book title is required");
        }
        if (book.getIsbn() == null || book.getIsbn().trim().isEmpty()) {
            throw new IllegalArgumentException("Book ISBN is required");
        }
        if (book.getTotalCopies() < 0) {
            throw new IllegalArgumentException("Total copies cannot be negative");
        }
        if (book.getAvailableCopies() < 0) {
            throw new IllegalArgumentException("Available copies cannot be negative");
        }
        if (book.getAvailableCopies() > book.getTotalCopies()) {
            throw new IllegalArgumentException("Available copies cannot exceed total copies");
        }
    }
}
