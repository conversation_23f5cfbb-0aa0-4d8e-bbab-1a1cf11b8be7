package com.akd.library.service;

import com.akd.library.model.Book;

import java.util.List;

/**
 * Created by am<PERSON><PERSON>
 */
public interface BookService {

    Book bookDetails(int id);

    List<Book> listAllBooks();

    Book addBook(Book book);

    Book updateBookDetails(Book book);

    void deleteBook(int id);

    List<Book> searchBookByTitle(String bookTitle);

    List<Book> searchBookByISBN(String ISBN);





}
