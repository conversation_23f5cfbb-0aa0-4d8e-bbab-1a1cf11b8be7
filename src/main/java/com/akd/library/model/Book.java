package com.akd.library.model;

import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;
import jakarta.persistence.*;

import java.time.Year;

/**
 * Created by am<PERSON><PERSON>
 */

@Entity
@Table(name = "books")
public class Book {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "book_id")
    private int bookId;
    private String title;
    private int author_id;
    private int publisher_id;
    private String isbn;
    private int category_id;
    private String language;
    @Column(name = "publication_year")
    private Year publication_year;
    @Column(name = "total_copies")
    private int totalCopies;
    @Column(name = "available_copies")
    private int availableCopies;
    @Column(name = "shelf_location")
    private String shelfLocation;

    public Book() {
    }

    public Book(int bookId, String title, int author_id, int publisher_id, String isbn, int category_id, String language, Year publication_year, int totalCopies, int availableCopies, String shelfLocation) {
        this.bookId = bookId;
        this.title = title;
        this.author_id = author_id;
        this.publisher_id = publisher_id;
        this.isbn = isbn;
        this.category_id = category_id;
        this.language = language;
        this.publication_year = publication_year;
        this.totalCopies = totalCopies;
        this.availableCopies = availableCopies;
        this.shelfLocation = shelfLocation;
    }

    @Override
    public String toString() {
        return "Book{" +
                "bookId=" + bookId +
                ", title='" + title + '\'' +
                ", author_id=" + author_id +
                ", publisher_id=" + publisher_id +
                ", isbn='" + isbn + '\'' +
                ", category_id=" + category_id +
                ", language='" + language + '\'' +
                ", publication_year=" + publication_year +
                ", totalCopies=" + totalCopies +
                ", availableCopies=" + availableCopies +
                ", shelfLocation='" + shelfLocation + '\'' +
                '}';
    }

    public int getBookId() {
        return bookId;
    }

    public void setBookId(int bookId) {
        this.bookId = bookId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public int getAuthor_id() {
        return author_id;
    }

    public void setAuthor_id(int author_id) {
        this.author_id = author_id;
    }

    public int getPublisher_id() {
        return publisher_id;
    }

    public void setPublisher_id(int publisher_id) {
        this.publisher_id = publisher_id;
    }

    public String getIsbn() {
        return isbn;
    }

    public void setIsbn(String isbn) {
        this.isbn = isbn;
    }

    public int getCategory_id() {
        return category_id;
    }

    public void setCategory_id(int category_id) {
        this.category_id = category_id;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public Year getPublication_year() {
        return publication_year;
    }

    public void setPublication_year(Year publication_year) {
        this.publication_year = publication_year;
    }

    public int getTotalCopies() {
        return totalCopies;
    }

    public void setTotalCopies(int totalCopies) {
        this.totalCopies = totalCopies;
    }

    public int getAvailableCopies() {
        return availableCopies;
    }

    public void setAvailableCopies(int availableCopies) {
        this.availableCopies = availableCopies;
    }

    public String getShelfLocation() {
        return shelfLocation;
    }

    public void setShelfLocation(String shelfLocation) {
        this.shelfLocation = shelfLocation;
    }
}
