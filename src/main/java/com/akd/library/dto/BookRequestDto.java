package com.akd.library.dto;

import jakarta.validation.constraints.*;
import java.time.Year;

/**
 * DTO for Book creation/update requests
 * Created by am<PERSON><PERSON>
 */
public class BookRequestDto {
    
    @NotBlank(message = "Title is required")
    @Size(max = 255, message = "Title must not exceed 255 characters")
    private String title;

    @Positive(message = "Author ID must be positive")
    private int authorId;

    @Positive(message = "Publisher ID must be positive")
    private int publisherId;

    @NotBlank(message = "ISBN is required")
    @Pattern(regexp = "^(?:ISBN(?:-1[03])?:? )?(?=[0-9X]{10}$|(?=(?:[0-9]+[- ]){3})[- 0-9X]{13}$|97[89][0-9]{10}$|(?=(?:[0-9]+[- ]){4})[- 0-9]{17}$)(?:97[89][- ]?)?[0-9]{1,5}[- ]?[0-9]+[- ]?[0-9]+[- ]?[0-9X]$", 
             message = "Invalid ISBN format")
    private String isbn;

    @Positive(message = "Category ID must be positive")
    private int categoryId;

    @NotBlank(message = "Language is required")
    @Size(max = 50, message = "Language must not exceed 50 characters")
    private String language;

    @NotNull(message = "Publication year is required")
    private Year publicationYear;

    @Min(value = 0, message = "Total copies must be non-negative")
    private int totalCopies;

    @Min(value = 0, message = "Available copies must be non-negative")
    private int availableCopies;

    @Size(max = 100, message = "Shelf location must not exceed 100 characters")
    private String shelfLocation;

    // Default constructor
    public BookRequestDto() {}

    // Getters and setters
    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }

    public int getAuthorId() { return authorId; }
    public void setAuthorId(int authorId) { this.authorId = authorId; }

    public int getPublisherId() { return publisherId; }
    public void setPublisherId(int publisherId) { this.publisherId = publisherId; }

    public String getIsbn() { return isbn; }
    public void setIsbn(String isbn) { this.isbn = isbn; }

    public int getCategoryId() { return categoryId; }
    public void setCategoryId(int categoryId) { this.categoryId = categoryId; }

    public String getLanguage() { return language; }
    public void setLanguage(String language) { this.language = language; }

    public Year getPublicationYear() { return publicationYear; }
    public void setPublicationYear(Year publicationYear) { this.publicationYear = publicationYear; }

    public int getTotalCopies() { return totalCopies; }
    public void setTotalCopies(int totalCopies) { this.totalCopies = totalCopies; }

    public int getAvailableCopies() { return availableCopies; }
    public void setAvailableCopies(int availableCopies) { this.availableCopies = availableCopies; }

    public String getShelfLocation() { return shelfLocation; }
    public void setShelfLocation(String shelfLocation) { this.shelfLocation = shelfLocation; }
}
