package com.akd.library.dto;

import java.time.Year;

/**
 * DTO for Book response
 * Created by am<PERSON><PERSON>
 */
public class BookResponseDto {
    private int bookId;
    private String title;
    private int authorId;
    private int publisherId;
    private String isbn;
    private int categoryId;
    private String language;
    private Year publicationYear;
    private int totalCopies;
    private int availableCopies;
    private String shelfLocation;

    // Default constructor
    public BookResponseDto() {}

    // Constructor with all fields
    public BookResponseDto(int bookId, String title, int authorId, int publisherId, 
                          String isbn, int categoryId, String language, Year publicationYear,
                          int totalCopies, int availableCopies, String shelfLocation) {
        this.bookId = bookId;
        this.title = title;
        this.authorId = authorId;
        this.publisherId = publisherId;
        this.isbn = isbn;
        this.categoryId = categoryId;
        this.language = language;
        this.publicationYear = publicationYear;
        this.totalCopies = totalCopies;
        this.availableCopies = availableCopies;
        this.shelfLocation = shelfLocation;
    }

    // Getters and setters
    public int getBookId() { return bookId; }
    public void setBookId(int bookId) { this.bookId = bookId; }

    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }

    public int getAuthorId() { return authorId; }
    public void setAuthorId(int authorId) { this.authorId = authorId; }

    public int getPublisherId() { return publisherId; }
    public void setPublisherId(int publisherId) { this.publisherId = publisherId; }

    public String getIsbn() { return isbn; }
    public void setIsbn(String isbn) { this.isbn = isbn; }

    public int getCategoryId() { return categoryId; }
    public void setCategoryId(int categoryId) { this.categoryId = categoryId; }

    public String getLanguage() { return language; }
    public void setLanguage(String language) { this.language = language; }

    public Year getPublicationYear() { return publicationYear; }
    public void setPublicationYear(Year publicationYear) { this.publicationYear = publicationYear; }

    public int getTotalCopies() { return totalCopies; }
    public void setTotalCopies(int totalCopies) { this.totalCopies = totalCopies; }

    public int getAvailableCopies() { return availableCopies; }
    public void setAvailableCopies(int availableCopies) { this.availableCopies = availableCopies; }

    public String getShelfLocation() { return shelfLocation; }
    public void setShelfLocation(String shelfLocation) { this.shelfLocation = shelfLocation; }
}
