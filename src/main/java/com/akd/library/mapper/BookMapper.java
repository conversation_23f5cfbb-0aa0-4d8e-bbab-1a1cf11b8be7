package com.akd.library.mapper;

import com.akd.library.dto.BookRequestDto;
import com.akd.library.dto.BookResponseDto;
import com.akd.library.model.Book;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper class for converting between Book entity and DTOs
 * Created by amitkumar
 */
@Component
public class BookMapper {

    /**
     * Convert Book entity to BookResponseDto
     * @param book Book entity
     * @return BookResponseDto
     */
    public BookResponseDto toResponseDto(Book book) {
        if (book == null) {
            return null;
        }
        
        return new BookResponseDto(
            book.getBookId(),
            book.getTitle(),
            book.getAuthor_id(),
            book.getPublisher_id(),
            book.getIsbn(),
            book.getCategory_id(),
            book.getLanguage(),
            book.getPublication_year(),
            book.getTotalCopies(),
            book.getAvailableCopies(),
            book.getShelfLocation()
        );
    }

    /**
     * Convert BookRequestDto to Book entity
     * @param requestDto BookRequestDto
     * @return Book entity
     */
    public Book toEntity(BookRequestDto requestDto) {
        if (requestDto == null) {
            return null;
        }
        
        Book book = new Book();
        book.setTitle(requestDto.getTitle());
        book.setAuthor_id(requestDto.getAuthorId());
        book.setPublisher_id(requestDto.getPublisherId());
        book.setIsbn(requestDto.getIsbn());
        book.setCategory_id(requestDto.getCategoryId());
        book.setLanguage(requestDto.getLanguage());
        book.setPublication_year(requestDto.getPublicationYear());
        book.setTotalCopies(requestDto.getTotalCopies());
        book.setAvailableCopies(requestDto.getAvailableCopies());
        book.setShelfLocation(requestDto.getShelfLocation());
        
        return book;
    }

    /**
     * Update existing Book entity with data from BookRequestDto
     * @param book Existing Book entity
     * @param requestDto BookRequestDto with updated data
     */
    public void updateEntityFromDto(Book book, BookRequestDto requestDto) {
        if (book == null || requestDto == null) {
            return;
        }
        
        book.setTitle(requestDto.getTitle());
        book.setAuthor_id(requestDto.getAuthorId());
        book.setPublisher_id(requestDto.getPublisherId());
        book.setIsbn(requestDto.getIsbn());
        book.setCategory_id(requestDto.getCategoryId());
        book.setLanguage(requestDto.getLanguage());
        book.setPublication_year(requestDto.getPublicationYear());
        book.setTotalCopies(requestDto.getTotalCopies());
        book.setAvailableCopies(requestDto.getAvailableCopies());
        book.setShelfLocation(requestDto.getShelfLocation());
    }

    /**
     * Convert list of Book entities to list of BookResponseDto
     * @param books List of Book entities
     * @return List of BookResponseDto
     */
    public List<BookResponseDto> toResponseDtoList(List<Book> books) {
        if (books == null) {
            return null;
        }
        
        return books.stream()
                .map(this::toResponseDto)
                .collect(Collectors.toList());
    }
}
