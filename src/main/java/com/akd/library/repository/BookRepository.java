package com.akd.library.repository;

import com.akd.library.model.Book;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Repository interface for Book entity
 * Created by amitkumar
 * User: ak139
 * Date: 14/07/25
 */
@Repository
public interface BookRepository extends JpaRepository<Book, Integer> {

    /**
     * Find books by title containing the given string (case-insensitive)
     * @param title Title to search for
     * @return List of books matching the title
     */
    List<Book> findByTitleContainingIgnoreCase(String title);

    /**
     * Find books by ISBN containing the given string (case-insensitive)
     * @param isbn ISBN to search for
     * @return List of books matching the ISBN
     */
    List<Book> findByIsbnContainingIgnoreCase(String isbn);

    /**
     * Find books by author ID
     * @param authorId Author ID
     * @return List of books by the author
     */
    List<Book> findByAuthor_id(int authorId);

    /**
     * Find books by category ID
     * @param categoryId Category ID
     * @return List of books in the category
     */
    List<Book> findByCategory_id(int categoryId);

    /**
     * Find available books (where available copies > 0)
     * @return List of available books
     */
    @Query("SELECT b FROM Book b WHERE b.availableCopies > 0")
    List<Book> findAvailableBooks();

    /**
     * Find books by language
     * @param language Language
     * @return List of books in the specified language
     */
    List<Book> findByLanguageIgnoreCase(String language);

    /**
     * Check if a book exists by ISBN
     * @param isbn ISBN to check
     * @return true if book exists, false otherwise
     */
    boolean existsByIsbn(String isbn);
}
