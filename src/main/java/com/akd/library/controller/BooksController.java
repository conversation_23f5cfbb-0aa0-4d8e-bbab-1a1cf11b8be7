package com.akd.library.controller;

import com.akd.library.model.Book;
import com.akd.library.service.BookService;
import jakarta.validation.Valid;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * REST Controller for managing books in the library system
 * Created by amitkumar
 * User: ak139
 */
@RestController
@RequestMapping("/api/v1/books")
public class BooksController {

    private final BookService bookService;

    public BooksController(BookService bookService) {
        this.bookService = bookService;
    }

    /**
     * Get all books
     * @return List of all books
     */
    @GetMapping
    public ResponseEntity<List<Book>> getAllBooks() {
        List<Book> books = bookService.listAllBooks();
        return ResponseEntity.ok(books);
    }

    /**
     * Get book by ID
     * @param id Book ID
     * @return Book details
     */
    @GetMapping("/{id}")
    public ResponseEntity<Book> getBookById(@PathVariable int id) {
        Book book = bookService.bookDetails(id);
        return ResponseEntity.ok(book);
    }

    /**
     * Add a new book
     * @param book Book to be added
     * @return Created book
     */
    @PostMapping
    public ResponseEntity<Book> addBook(@Valid @RequestBody Book book) {
        Book savedBook = bookService.addBook(book);
        return ResponseEntity.status(HttpStatus.CREATED).body(savedBook);
    }

    /**
     * Update book details
     * @param id Book ID
     * @param book Updated book details
     * @return Updated book
     */
    @PutMapping("/{id}")
    public ResponseEntity<Book> updateBook(@PathVariable int id, @Valid @RequestBody Book book) {
        book.setBookId(id);
        Book updatedBook = bookService.updateBookDetails(book);
        return ResponseEntity.ok(updatedBook);
    }

    /**
     * Delete a book
     * @param id Book ID
     * @return No content
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteBook(@PathVariable int id) {
        bookService.deleteBook(id);
        return ResponseEntity.noContent().build();
    }

    /**
     * Search books by title
     * @param title Book title
     * @return List of books matching the title
     */
    @GetMapping("/search/title")
    public ResponseEntity<List<Book>> searchBooksByTitle(@RequestParam String title) {
        List<Book> books = bookService.searchBookByTitle(title);
        return ResponseEntity.ok(books);
    }

    /**
     * Search books by ISBN
     * @param isbn Book ISBN
     * @return List of books matching the ISBN
     */
    @GetMapping("/search/isbn")
    public ResponseEntity<List<Book>> searchBooksByISBN(@RequestParam String isbn) {
        List<Book> books = bookService.searchBookByISBN(isbn);
        return ResponseEntity.ok(books);
    }
}
