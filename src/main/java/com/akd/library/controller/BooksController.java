package com.akd.library.controller;

import com.akd.library.dto.BookRequestDto;
import com.akd.library.dto.BookResponseDto;
import com.akd.library.mapper.BookMapper;
import com.akd.library.model.Book;
import com.akd.library.service.BookService;
import jakarta.validation.Valid;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * REST Controller for managing books in the library system
 * Created by amitkumar
 * User: ak139
 */
@RestController
@RequestMapping("/api/v1/books")
public class BooksController {

    private final BookService bookService;
    private final BookMapper bookMapper;

    public BooksController(BookService bookService, BookMapper bookMapper) {
        this.bookService = bookService;
        this.bookMapper = bookMapper;
    }

    /**
     * Get all books
     * @return List of all books
     */
    @GetMapping
    public ResponseEntity<List<BookResponseDto>> getAllBooks() {
        List<Book> books = bookService.listAllBooks();
        List<BookResponseDto> responseDtos = bookMapper.toResponseDtoList(books);
        return ResponseEntity.ok(responseDtos);
    }

    /**
     * Get book by ID
     * @param id Book ID
     * @return Book details
     */
    @GetMapping("/{id}")
    public ResponseEntity<BookResponseDto> getBookById(@PathVariable int id) {
        Book book = bookService.bookDetails(id);
        BookResponseDto responseDto = bookMapper.toResponseDto(book);
        return ResponseEntity.ok(responseDto);
    }

    /**
     * Add a new book
     * @param bookRequestDto Book request data
     * @return Created book
     */
    @PostMapping
    public ResponseEntity<BookResponseDto> addBook(@Valid @RequestBody BookRequestDto bookRequestDto) {
        Book book = bookMapper.toEntity(bookRequestDto);
        Book savedBook = bookService.addBook(book);
        BookResponseDto responseDto = bookMapper.toResponseDto(savedBook);
        return ResponseEntity.status(HttpStatus.CREATED).body(responseDto);
    }

    /**
     * Update book details
     * @param id Book ID
     * @param bookRequestDto Updated book details
     * @return Updated book
     */
    @PutMapping("/{id}")
    public ResponseEntity<BookResponseDto> updateBook(@PathVariable int id, @Valid @RequestBody BookRequestDto bookRequestDto) {
        Book book = bookMapper.toEntity(bookRequestDto);
        book.setBookId(id);
        Book updatedBook = bookService.updateBookDetails(book);
        BookResponseDto responseDto = bookMapper.toResponseDto(updatedBook);
        return ResponseEntity.ok(responseDto);
    }

    /**
     * Delete a book
     * @param id Book ID
     * @return No content
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteBook(@PathVariable int id) {
        bookService.deleteBook(id);
        return ResponseEntity.noContent().build();
    }

    /**
     * Search books by title
     * @param title Book title
     * @return List of books matching the title
     */
    @GetMapping("/search/title")
    public ResponseEntity<List<BookResponseDto>> searchBooksByTitle(@RequestParam String title) {
        List<Book> books = bookService.searchBookByTitle(title);
        List<BookResponseDto> responseDtos = bookMapper.toResponseDtoList(books);
        return ResponseEntity.ok(responseDtos);
    }

    /**
     * Search books by ISBN
     * @param isbn Book ISBN
     * @return List of books matching the ISBN
     */
    @GetMapping("/search/isbn")
    public ResponseEntity<List<BookResponseDto>> searchBooksByISBN(@RequestParam String isbn) {
        List<Book> books = bookService.searchBookByISBN(isbn);
        List<BookResponseDto> responseDtos = bookMapper.toResponseDtoList(books);
        return ResponseEntity.ok(responseDtos);
    }
}
