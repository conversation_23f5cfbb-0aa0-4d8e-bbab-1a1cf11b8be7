package com.akd.library.controller;

import com.akd.library.model.Book;
import com.akd.library.repository.BookRepository;
import com.akd.library.service.BookService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Created by am<PERSON><PERSON>
 * User: ak139
 */
@RestController
@RequestMapping("v1/books")
public class BooksController {

    BookService bookService;

    public BooksController(BookService bookService, BookRepository bookRepository) {
        this.bookService = bookService;
    }

    @GetMapping
    public List<Book> allBooks(){
        return bookService.listAllBooks();
    }
   @GetMapping("/{id}")
    public Book getBook(@PathVariable int id){
        return bookService.bookDetails(id);
    }

    @PostMapping()
    public void addBook(@RequestBody Book book){
        bookService.addBook(book);

    }
}
