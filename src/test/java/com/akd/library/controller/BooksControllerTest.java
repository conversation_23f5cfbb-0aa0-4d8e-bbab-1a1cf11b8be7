package com.akd.library.controller;

import com.akd.library.exception.BookNotFoundException;
import com.akd.library.model.Book;
import com.akd.library.service.BookService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.time.Year;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Unit tests for BooksController
 */
@WebMvcTest(BooksController.class)
class BooksControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private BookService bookService;

    @Autowired
    private ObjectMapper objectMapper;

    private Book testBook;

    @BeforeEach
    void setUp() {
        testBook = new Book();
        testBook.setBookId(1);
        testBook.setTitle("Test Book");
        testBook.setAuthor_id(1);
        testBook.setPublisher_id(1);
        testBook.setIsbn("978-0-123456-78-9");
        testBook.setCategory_id(1);
        testBook.setLanguage("English");
        testBook.setPublication_year(Year.of(2023));
        testBook.setTotalCopies(10);
        testBook.setAvailableCopies(5);
        testBook.setShelfLocation("A1-B2");
    }

    @Test
    void getAllBooks_ShouldReturnListOfBooks() throws Exception {
        List<Book> books = Arrays.asList(testBook);
        when(bookService.listAllBooks()).thenReturn(books);

        mockMvc.perform(get("/api/v1/books"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$[0].title").value("Test Book"));

        verify(bookService).listAllBooks();
    }

    @Test
    void getBookById_ShouldReturnBook_WhenBookExists() throws Exception {
        when(bookService.bookDetails(1)).thenReturn(testBook);

        mockMvc.perform(get("/api/v1/books/1"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.title").value("Test Book"));

        verify(bookService).bookDetails(1);
    }

    @Test
    void getBookById_ShouldReturnNotFound_WhenBookDoesNotExist() throws Exception {
        when(bookService.bookDetails(999)).thenThrow(new BookNotFoundException("Book not found with id: 999"));

        mockMvc.perform(get("/api/v1/books/999"))
                .andExpect(status().isNotFound());

        verify(bookService).bookDetails(999);
    }

    @Test
    void addBook_ShouldCreateBook_WhenValidInput() throws Exception {
        when(bookService.addBook(any(Book.class))).thenReturn(testBook);

        mockMvc.perform(post("/api/v1/books")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testBook)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.title").value("Test Book"));

        verify(bookService).addBook(any(Book.class));
    }

    @Test
    void updateBook_ShouldUpdateBook_WhenValidInput() throws Exception {
        when(bookService.updateBookDetails(any(Book.class))).thenReturn(testBook);

        mockMvc.perform(put("/api/v1/books/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testBook)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.title").value("Test Book"));

        verify(bookService).updateBookDetails(any(Book.class));
    }

    @Test
    void deleteBook_ShouldDeleteBook_WhenBookExists() throws Exception {
        doNothing().when(bookService).deleteBook(1);

        mockMvc.perform(delete("/api/v1/books/1"))
                .andExpect(status().isNoContent());

        verify(bookService).deleteBook(1);
    }

    @Test
    void searchBooksByTitle_ShouldReturnBooks() throws Exception {
        List<Book> books = Arrays.asList(testBook);
        when(bookService.searchBookByTitle("Test")).thenReturn(books);

        mockMvc.perform(get("/api/v1/books/search/title")
                        .param("title", "Test"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[0].title").value("Test Book"));

        verify(bookService).searchBookByTitle("Test");
    }

    @Test
    void searchBooksByISBN_ShouldReturnBooks() throws Exception {
        List<Book> books = Arrays.asList(testBook);
        when(bookService.searchBookByISBN("978-0-123456-78-9")).thenReturn(books);

        mockMvc.perform(get("/api/v1/books/search/isbn")
                        .param("isbn", "978-0-123456-78-9"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[0].isbn").value("978-0-123456-78-9"));

        verify(bookService).searchBookByISBN("978-0-123456-78-9");
    }
}
