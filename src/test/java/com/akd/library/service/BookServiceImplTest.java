package com.akd.library.service;

import com.akd.library.exception.BookNotFoundException;
import com.akd.library.model.Book;
import com.akd.library.repository.BookRepository;
import com.akd.library.service.impl.BookServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Year;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for BookServiceImpl
 */
@ExtendWith(MockitoExtension.class)
class BookServiceImplTest {

    @Mock
    private BookRepository bookRepository;

    @InjectMocks
    private BookServiceImpl bookService;

    private Book testBook;

    @BeforeEach
    void setUp() {
        testBook = new Book();
        testBook.setBookId(1);
        testBook.setTitle("Test Book");
        testBook.setAuthor_id(1);
        testBook.setPublisher_id(1);
        testBook.setIsbn("978-0-123456-78-9");
        testBook.setCategory_id(1);
        testBook.setLanguage("English");
        testBook.setPublication_year(Year.of(2023));
        testBook.setTotalCopies(10);
        testBook.setAvailableCopies(5);
        testBook.setShelfLocation("A1-B2");
    }

    @Test
    void bookDetails_ShouldReturnBook_WhenBookExists() {
        when(bookRepository.findById(1)).thenReturn(Optional.of(testBook));

        Book result = bookService.bookDetails(1);

        assertNotNull(result);
        assertEquals("Test Book", result.getTitle());
        verify(bookRepository).findById(1);
    }

    @Test
    void bookDetails_ShouldThrowException_WhenBookNotFound() {
        when(bookRepository.findById(999)).thenReturn(Optional.empty());

        assertThrows(BookNotFoundException.class, () -> bookService.bookDetails(999));
        verify(bookRepository).findById(999);
    }

    @Test
    void listAllBooks_ShouldReturnAllBooks() {
        List<Book> books = Arrays.asList(testBook);
        when(bookRepository.findAll()).thenReturn(books);

        List<Book> result = bookService.listAllBooks();

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("Test Book", result.get(0).getTitle());
        verify(bookRepository).findAll();
    }

    @Test
    void addBook_ShouldSaveBook_WhenValidBook() {
        when(bookRepository.save(any(Book.class))).thenReturn(testBook);

        Book result = bookService.addBook(testBook);

        assertNotNull(result);
        assertEquals("Test Book", result.getTitle());
        verify(bookRepository).save(testBook);
    }

    @Test
    void addBook_ShouldThrowException_WhenTitleIsNull() {
        testBook.setTitle(null);

        assertThrows(IllegalArgumentException.class, () -> bookService.addBook(testBook));
        verify(bookRepository, never()).save(any());
    }

    @Test
    void addBook_ShouldThrowException_WhenAvailableCopiesExceedTotal() {
        testBook.setTotalCopies(5);
        testBook.setAvailableCopies(10);

        assertThrows(IllegalArgumentException.class, () -> bookService.addBook(testBook));
        verify(bookRepository, never()).save(any());
    }

    @Test
    void updateBookDetails_ShouldUpdateBook_WhenBookExists() {
        when(bookRepository.existsById(1)).thenReturn(true);
        when(bookRepository.save(any(Book.class))).thenReturn(testBook);

        Book result = bookService.updateBookDetails(testBook);

        assertNotNull(result);
        assertEquals("Test Book", result.getTitle());
        verify(bookRepository).existsById(1);
        verify(bookRepository).save(testBook);
    }

    @Test
    void updateBookDetails_ShouldThrowException_WhenBookNotFound() {
        when(bookRepository.existsById(999)).thenReturn(false);
        testBook.setBookId(999);

        assertThrows(BookNotFoundException.class, () -> bookService.updateBookDetails(testBook));
        verify(bookRepository).existsById(999);
        verify(bookRepository, never()).save(any());
    }

    @Test
    void deleteBook_ShouldDeleteBook_WhenBookExists() {
        when(bookRepository.existsById(1)).thenReturn(true);

        bookService.deleteBook(1);

        verify(bookRepository).existsById(1);
        verify(bookRepository).deleteById(1);
    }

    @Test
    void deleteBook_ShouldThrowException_WhenBookNotFound() {
        when(bookRepository.existsById(999)).thenReturn(false);

        assertThrows(BookNotFoundException.class, () -> bookService.deleteBook(999));
        verify(bookRepository).existsById(999);
        verify(bookRepository, never()).deleteById(any());
    }

    @Test
    void searchBookByTitle_ShouldReturnBooks() {
        List<Book> books = Arrays.asList(testBook);
        when(bookRepository.findByTitleContainingIgnoreCase("Test")).thenReturn(books);

        List<Book> result = bookService.searchBookByTitle("Test");

        assertNotNull(result);
        assertEquals(1, result.size());
        verify(bookRepository).findByTitleContainingIgnoreCase("Test");
    }

    @Test
    void searchBookByTitle_ShouldThrowException_WhenTitleIsNull() {
        assertThrows(IllegalArgumentException.class, () -> bookService.searchBookByTitle(null));
        verify(bookRepository, never()).findByTitleContainingIgnoreCase(any());
    }

    @Test
    void searchBookByISBN_ShouldReturnBooks() {
        List<Book> books = Arrays.asList(testBook);
        when(bookRepository.findByIsbnContainingIgnoreCase("978")).thenReturn(books);

        List<Book> result = bookService.searchBookByISBN("978");

        assertNotNull(result);
        assertEquals(1, result.size());
        verify(bookRepository).findByIsbnContainingIgnoreCase("978");
    }

    @Test
    void searchBookByISBN_ShouldThrowException_WhenISBNIsEmpty() {
        assertThrows(IllegalArgumentException.class, () -> bookService.searchBookByISBN(""));
        verify(bookRepository, never()).findByIsbnContainingIgnoreCase(any());
    }
}
