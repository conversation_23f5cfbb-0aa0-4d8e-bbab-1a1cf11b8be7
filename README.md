# Library Management System

A Spring Boot REST API for managing books in a library system.

## Features

- CRUD operations for books
- Search functionality by title and ISBN
- Input validation
- Global exception handling
- Comprehensive unit tests

## API Endpoints

### Books API

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/v1/books` | Get all books |
| GET | `/api/v1/books/{id}` | Get book by ID |
| POST | `/api/v1/books` | Add a new book |
| PUT | `/api/v1/books/{id}` | Update book details |
| DELETE | `/api/v1/books/{id}` | Delete a book |
| GET | `/api/v1/books/search/title?title={title}` | Search books by title |
| GET | `/api/v1/books/search/isbn?isbn={isbn}` | Search books by ISBN |

### Request/Response Examples

#### Get All Books
```http
GET /api/v1/books
```

Response:
```json
[
  {
    "bookId": 1,
    "title": "Spring Boot in Action",
    "author_id": 1,
    "publisher_id": 1,
    "isbn": "978-1-617291-20-3",
    "category_id": 1,
    "language": "English",
    "publication_year": 2015,
    "totalCopies": 10,
    "availableCopies": 5,
    "shelfLocation": "A1-B2"
  }
]
```

#### Add New Book
```http
POST /api/v1/books
Content-Type: application/json

{
  "title": "Spring Boot in Action",
  "author_id": 1,
  "publisher_id": 1,
  "isbn": "978-1-617291-20-3",
  "category_id": 1,
  "language": "English",
  "publication_year": 2015,
  "totalCopies": 10,
  "availableCopies": 5,
  "shelfLocation": "A1-B2"
}
```

## Validation Rules

- **Title**: Required, max 255 characters
- **ISBN**: Required, valid ISBN format
- **Author ID**: Must be positive
- **Publisher ID**: Must be positive
- **Category ID**: Must be positive
- **Language**: Required, max 50 characters
- **Publication Year**: Required
- **Total Copies**: Must be non-negative
- **Available Copies**: Must be non-negative and not exceed total copies
- **Shelf Location**: Max 100 characters

## Error Handling

The API returns appropriate HTTP status codes and error messages:

- `200 OK` - Successful GET requests
- `201 Created` - Successful POST requests
- `204 No Content` - Successful DELETE requests
- `400 Bad Request` - Validation errors or invalid input
- `404 Not Found` - Resource not found
- `500 Internal Server Error` - Unexpected errors

### Error Response Format

```json
{
  "status": 404,
  "message": "Book not found with id: 999",
  "timestamp": "2023-07-30T10:30:00"
}
```

### Validation Error Response Format

```json
{
  "status": 400,
  "message": "Validation failed",
  "timestamp": "2023-07-30T10:30:00",
  "fieldErrors": {
    "title": "Title is required",
    "isbn": "Invalid ISBN format"
  }
}
```

## Running the Application

1. Ensure MySQL is running on localhost:3306
2. Create a database named `library`
3. Update database credentials in `application.properties`
4. Run the application:

```bash
./mvnw spring-boot:run
```

## Running Tests

```bash
./mvnw test
```

## Technology Stack

- Spring Boot 3.5.3
- Spring Data JPA
- Spring Web
- Spring Validation
- MySQL
- JUnit 5
- Mockito
- Maven
